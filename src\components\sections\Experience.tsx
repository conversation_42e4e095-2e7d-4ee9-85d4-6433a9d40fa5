'use client';

import { motion } from 'framer-motion';
import { FaBriefcase, FaCalendarAlt } from 'react-icons/fa';
import { EXPERIENCE, ANIMATION_VARIANTS } from '@/lib/constants';
import Section from '@/components/ui/Section';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

export default function Experience() {
  return (
    <Section
      id="experience"
      background="primary"
      className="relative"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/20 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-secondary/20 rounded-full blur-3xl" />
      </div>
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={ANIMATION_VARIANTS.stagger}
        className="relative z-10 space-y-16"
      >
          {/* Section Header */}
          <motion.div
            variants={ANIMATION_VARIANTS.fadeIn}
            className="text-center"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gradient mb-4">
              Experience
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              My professional journey in software development
            </p>
          </motion.div>

          {/* Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-secondary to-accent" />

            <div className="space-y-16">
              {EXPERIENCE.map((exp, index) => (
                <motion.div
                  key={exp.id}
                  variants={ANIMATION_VARIANTS.slideUp}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className={`relative flex items-center ${
                    index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                  }`}
                >
                  {/* Timeline Dot */}
                  <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-bg-primary z-10">
                    <div className="absolute inset-0 bg-primary rounded-full animate-ping opacity-75" />
                  </div>

                  {/* Content Card */}
                  <motion.div
                    whileHover={{ scale: 1.02, boxShadow: "0 0 30px rgba(0, 255, 204, 0.2)" }}
                    className={`ml-16 md:ml-0 md:w-5/12 ${
                      index % 2 === 0 ? 'md:mr-auto md:pr-8' : 'md:ml-auto md:pl-8'
                    }`}
                  >
                    <div className="glass p-6 rounded-xl border border-white/10 hover:border-primary/30 transition-all duration-300">
                      {/* Company & Position */}
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-bold text-white mb-1">
                            {exp.position}
                          </h3>
                          <h4 className="text-lg text-primary font-semibold">
                            {exp.company}
                          </h4>
                        </div>
                        {exp.current && (
                          <span className="px-3 py-1 bg-primary/20 text-primary text-xs font-semibold rounded-full border border-primary/30">
                            Current
                          </span>
                        )}
                      </div>

                      {/* Duration */}
                      <div className="flex items-center text-gray-400 mb-4">
                        <FaCalendarAlt className="mr-2" />
                        <span className="text-sm">{exp.duration}</span>
                      </div>

                      {/* Description */}
                      <p className="text-gray-300 leading-relaxed mb-4">
                        {exp.description}
                      </p>

                      {/* Technologies */}
                      <div className="flex flex-wrap gap-2">
                        {exp.technologies.map((tech) => (
                          <motion.span
                            key={tech}
                            whileHover={{ scale: 1.1 }}
                            className="px-3 py-1 bg-bg-tertiary text-primary text-xs font-medium rounded-full border border-primary/20 hover:border-primary/50 transition-all duration-300"
                          >
                            {tech}
                          </motion.span>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <motion.div
            variants={ANIMATION_VARIANTS.fadeIn}
            className="text-center"
          >
            <div className="glass p-8 rounded-xl max-w-2xl mx-auto">
              <FaBriefcase className="text-primary text-4xl mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-4">
                Ready for New Opportunities
              </h3>
              <p className="text-gray-300 mb-6">
                I&apos;m always excited to work on challenging projects and collaborate with amazing teams.
                Let&apos;s build something incredible together!
              </p>
              <motion.button
                whileHover={{ scale: 1.05, boxShadow: "0 0 25px rgba(0, 255, 204, 0.5)" }}
                whileTap={{ scale: 0.95 }}
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="btn-primary"
              >
                Let&apos;s Connect
              </motion.button>
            </div>
          </motion.div>
      </motion.div>
    </Section>
  );
}
