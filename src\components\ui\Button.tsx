import { ReactNode, forwardRef } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ButtonProps extends Omit<HTMLMotionProps<"button">, 'children'> {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
}

const buttonVariants = {
  primary: 'bg-primary text-bg-primary hover:bg-primary/90 hover:shadow-glow',
  secondary: 'bg-secondary text-white hover:bg-secondary/90 hover:shadow-glow-secondary',
  outline: 'bg-transparent text-primary border-2 border-primary hover:bg-primary hover:text-bg-primary',
  ghost: 'bg-transparent text-gray-300 hover:text-white hover:bg-white/10',
  gradient: 'bg-gradient-to-r from-primary to-accent text-bg-primary hover:from-primary/90 hover:to-accent/90 hover:shadow-glow'
};

const buttonSizes = {
  sm: 'px-4 py-2 text-sm',
  md: 'px-6 py-3 text-base',
  lg: 'px-8 py-4 text-lg',
  xl: 'px-10 py-5 text-xl'
};

const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  loading = false,
  disabled = false,
  className = '',
  ...props
}, ref) => {
  const baseClasses = cn(
    'inline-flex items-center justify-center font-semibold rounded-lg',
    'transition-all duration-300 transform',
    'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-bg-primary',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none',
    buttonVariants[variant],
    buttonSizes[size],
    {
      'w-full': fullWidth,
      'hover:scale-105 active:scale-95': !disabled && !loading,
      'cursor-not-allowed': disabled || loading,
    },
    className
  );

  return (
    <motion.button
      ref={ref}
      className={baseClasses}
      disabled={disabled || loading}
      whileHover={!disabled && !loading ? { scale: 1.05 } : {}}
      whileTap={!disabled && !loading ? { scale: 0.95 } : {}}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
      {...props}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-3 h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </motion.button>
  );
});

Button.displayName = 'Button';

export default Button;
