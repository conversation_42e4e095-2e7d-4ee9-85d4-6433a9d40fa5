@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/* Custom CSS Variables */
:root {
  --color-primary: #00ffcc;
  --color-secondary: #ff3366;
  --color-accent: #00d4ff;
  --color-bg-primary: #0a0a0a;
  --color-bg-secondary: #111111;
  --color-bg-tertiary: #1a1a1a;
  --color-text-primary: #ffffff;
  --color-text-secondary: #a0a0a0;
  --color-text-muted: #666666;
  --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  --gradient-secondary: linear-gradient(135deg, var(--color-secondary), #ff6b9d);
  --shadow-glow: 0 0 20px rgba(0, 255, 204, 0.3);
  --shadow-glow-secondary: 0 0 20px rgba(255, 51, 102, 0.3);

  /* Enhanced 8px Grid Spacing System */
  --spacing-0: 0;          /* 0px */
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-12: 3rem;      /* 48px */
  --spacing-16: 4rem;      /* 64px */
  --spacing-20: 5rem;      /* 80px */
  --spacing-24: 6rem;      /* 96px */
  --spacing-32: 8rem;      /* 128px */
  --spacing-40: 10rem;     /* 160px */

  /* Legacy spacing for backward compatibility */
  --spacing-xs: var(--spacing-2);
  --spacing-sm: var(--spacing-4);
  --spacing-md: var(--spacing-6);
  --spacing-lg: var(--spacing-8);
  --spacing-xl: var(--spacing-12);
  --spacing-2xl: var(--spacing-16);
  --spacing-3xl: var(--spacing-24);
  --spacing-4xl: var(--spacing-32);

  /* Container Widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* Grid System */
  --grid-gap: var(--spacing-8);
  --grid-gap-sm: var(--spacing-6);
  --grid-gap-xs: var(--spacing-4);

  /* Border Radius System */
  --radius-none: 0;
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.5rem;     /* 8px */
  --radius-lg: 0.75rem;    /* 12px */
  --radius-xl: 1rem;       /* 16px */
  --radius-2xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent);
}

/* Selection */
::selection {
  background: var(--color-primary);
  color: var(--color-bg-primary);
}

/* Utility Classes */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow {
  box-shadow: var(--shadow-glow);
}

.glow-secondary {
  box-shadow: var(--shadow-glow-secondary);
}

.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Layout Utilities */
.container-custom {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

@media (min-width: 640px) {
  .container-custom {
    max-width: var(--container-sm);
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }
}

@media (min-width: 768px) {
  .container-custom {
    max-width: var(--container-md);
  }
}

@media (min-width: 1024px) {
  .container-custom {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1280px) {
  .container-custom {
    max-width: var(--container-xl);
  }
}

@media (min-width: 1536px) {
  .container-custom {
    max-width: var(--container-2xl);
  }
}

/* Enhanced Section Spacing System */
.section-padding-none {
  padding-top: 0;
  padding-bottom: 0;
}

.section-padding-sm {
  padding-top: var(--spacing-12);
  padding-bottom: var(--spacing-12);
}

.section-padding-md {
  padding-top: var(--spacing-16);
  padding-bottom: var(--spacing-16);
}

.section-padding-lg {
  padding-top: var(--spacing-20);
  padding-bottom: var(--spacing-20);
}

.section-padding-xl {
  padding-top: var(--spacing-24);
  padding-bottom: var(--spacing-24);
}

.section-padding {
  padding-top: var(--spacing-24);
  padding-bottom: var(--spacing-24);
}

@media (max-width: 768px) {
  .section-padding-sm {
    padding-top: var(--spacing-10);
    padding-bottom: var(--spacing-10);
  }

  .section-padding-md {
    padding-top: var(--spacing-12);
    padding-bottom: var(--spacing-12);
  }

  .section-padding-lg {
    padding-top: var(--spacing-16);
    padding-bottom: var(--spacing-16);
  }

  .section-padding-xl {
    padding-top: var(--spacing-20);
    padding-bottom: var(--spacing-20);
  }

  .section-padding {
    padding-top: var(--spacing-20);
    padding-bottom: var(--spacing-20);
  }
}

/* Grid Utilities */
.grid-responsive {
  display: grid;
  gap: var(--grid-gap);
}

@media (max-width: 768px) {
  .grid-responsive {
    gap: var(--grid-gap-sm);
  }
}

/* Perfect Center */
.perfect-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Consistent Button Styles */
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--gradient-primary);
  color: var(--color-bg-primary);
  font-weight: 600;
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  background: transparent;
  color: var(--color-text-primary);
  font-weight: 600;
  border-radius: var(--radius-lg);
  border: 2px solid var(--color-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-secondary:hover {
  background: var(--color-primary);
  color: var(--color-bg-primary);
  transform: translateY(-2px);
}

/* Animation Classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 5px var(--color-primary); }
  100% { box-shadow: 0 0 20px var(--color-primary), 0 0 30px var(--color-primary); }
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Responsive Typography */
.text-responsive-xl {
  font-size: clamp(2rem, 5vw, 4rem);
}

.text-responsive-lg {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
}

.text-responsive-md {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
}

/* Loading Animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}
