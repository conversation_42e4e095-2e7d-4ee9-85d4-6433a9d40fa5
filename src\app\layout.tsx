import type { Metadata } from "next";
import "./globals.css";
import { PERSONAL_INFO } from "@/lib/constants";

export const metadata: Metadata = {
  title: `${PERSONAL_INFO.name} - ${PERSONAL_INFO.title}`,
  description: `${PERSONAL_INFO.subtitle} - Portfolio of ${PERSONAL_INFO.name}, a passionate developer and future space scientist.`,
  keywords: ["developer", "portfolio", "space science", "technology", "React", "Next.js", "TypeScript"],
  authors: [{ name: PERSONAL_INFO.name, url: PERSONAL_INFO.github }],
  creator: PERSONAL_INFO.name,
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://talibshaikh.dev", // Update with actual domain
    title: `${PERSONAL_INFO.name} - ${PERSONAL_INFO.title}`,
    description: `${PERSONAL_INFO.subtitle} - Portfolio of ${PERSONAL_INFO.name}`,
    siteName: `${PERSONAL_INFO.name} Portfolio`,
  },
  twitter: {
    card: "summary_large_image",
    title: `${PERSONAL_INFO.name} - ${PERSONAL_INFO.title}`,
    description: `${PERSONAL_INFO.subtitle} - Portfolio of ${PERSONAL_INFO.name}`,
    creator: "@talibshaib", // Update with actual Twitter handle
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="font-sans antialiased bg-bg-primary text-white overflow-x-hidden">
        <div className="relative min-h-screen">
          {children}
        </div>
      </body>
    </html>
  );
}
