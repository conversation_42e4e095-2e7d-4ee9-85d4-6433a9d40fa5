# 🚀 Talib Shaikh - Portfolio Website

A sleek, modern, and highly interactive portfolio website inspired by [dunks1980.com](https://dunks1980.com), showcasing the skills and projects of <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>, Tech Enthusiast, and Future Space Scientist.

## ✨ Features

- **🎨 Modern Design**: Dark-themed aesthetic with neon accent colors (#00ffcc, #ff3366)
- **🌟 Interactive Animations**: Smooth transitions and scroll-triggered animations using Framer Motion
- **🌌 3D Elements**: Space-themed 3D components using Three.js and React Three Fiber
- **📱 Fully Responsive**: Optimized for all screen sizes and devices
- **⚡ Performance Optimized**: Built with Next.js 15 and Tailwind CSS 4
- **🎯 Smooth Navigation**: Single-page application with smooth scroll navigation
- **✨ Particle Effects**: Interactive particle background system
- **🔧 TypeScript**: Fully typed for better development experience

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS 4
- **Animations**: Framer Motion
- **3D Graphics**: Three.js with React Three Fiber
- **Icons**: React Icons
- **State Management**: Zustand
- **Database**: Supabase (configured)
- **Language**: TypeScript
- **Deployment**: Vercel-ready

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone https://github.com/Talibshaib/folio.git
   cd folio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles and CSS variables
│   ├── layout.tsx         # Root layout with metadata
│   └── page.tsx           # Main portfolio page
├── components/
│   ├── layout/            # Layout components
│   │   ├── Navbar.tsx     # Navigation with smooth scroll
│   │   └── Footer.tsx     # Footer with social links
│   ├── sections/          # Page sections
│   │   ├── Hero.tsx       # Hero section with 3D elements
│   │   ├── About.tsx      # About section with skills
│   │   ├── Experience.tsx # Experience timeline
│   │   ├── Projects.tsx   # Projects showcase with filtering
│   │   └── Contact.tsx    # Contact form and info
│   ├── ui/                # Reusable UI components
│   │   ├── AnimatedText.tsx      # Text animation component
│   │   ├── ParticleBackground.tsx # Interactive particles
│   │   └── ScrollIndicator.tsx   # Scroll progress indicator
│   └── 3d/                # 3D components
│       └── SpaceScene.tsx # Space-themed 3D scene
├── hooks/                 # Custom React hooks
│   └── useScrollAnimation.ts # Scroll-based animations
├── lib/                   # Utilities and constants
│   └── constants.ts       # App constants and data
└── store/                 # State management (Zustand)
```

## 🎨 Design System

### Color Palette
- **Primary**: #00ffcc (Neon Cyan)
- **Secondary**: #ff3366 (Electric Pink)
- **Accent**: #00d4ff (Bright Blue)
- **Background**: #0a0a0a, #111111, #1a1a1a (Dark Grays)

### Typography
- **Primary Font**: Inter (Google Fonts)
- **Monospace**: JetBrains Mono (Google Fonts)

### Animations
- Smooth scroll navigation
- Parallax effects
- Hover animations
- Loading states
- Scroll-triggered reveals

## 📝 Customization

### Personal Information
Update your details in `src/lib/constants.ts`:

```typescript
export const PERSONAL_INFO = {
  name: "Your Name",
  title: "Your Title",
  email: "<EMAIL>",
  // ... other details
};
```

### Projects
Add your projects in the `PROJECTS` array in `src/lib/constants.ts`

### Experience
Update your work experience in the `EXPERIENCE` array

### Skills
Modify the `SKILLS` object to reflect your technical skills

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with zero configuration

### Other Platforms
The project is compatible with any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform

## 🔧 Environment Variables

Create a `.env.local` file for any environment-specific configurations:

```env
# Add your environment variables here
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions, issues, and feature requests are welcome! Feel free to check the [issues page](https://github.com/Talibshaib/folio/issues).

## 📞 Contact

**Talib Shaikh**
- Email: <EMAIL>
- GitHub: [@Talibshaib](https://github.com/Talibshaib)
- LinkedIn: [Talib Shaikh](https://linkedin.com/in/talibshaib)

---

⭐ If you found this portfolio helpful, please give it a star on GitHub!
