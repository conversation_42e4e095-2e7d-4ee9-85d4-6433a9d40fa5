'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaGithub, FaExternalLinkAlt, FaFilter } from 'react-icons/fa';
import { PROJECTS, ANIMATION_VARIANTS } from '@/lib/constants';
import Section from '@/components/ui/Section';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

type TechnologyFilter = string | 'all';

export default function Projects() {
  const [filter, setFilter] = useState<TechnologyFilter>('all');
  const [hoveredProject, setHoveredProject] = useState<number | null>(null);

  // Get unique technologies for filter
  const allTechnologies = Array.from(
    new Set(PROJECTS.flatMap(project => [...project.technologies]))
  );

  const filteredProjects = filter === 'all'
    ? PROJECTS
    : PROJECTS.filter(project =>
        [...project.technologies].some(tech => tech === filter)
      );

  return (
    <Section
      id="projects"
      background="secondary"
      className="relative"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(0,255,204,0.1),transparent_50%)]" />
      </div>
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={ANIMATION_VARIANTS.stagger}
        className="relative z-10 space-y-20"
      >
          {/* Section Header */}
          <motion.div
            variants={ANIMATION_VARIANTS.fadeIn}
            className="text-center"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gradient mb-4">
              My Projects
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              A collection of projects showcasing my skills and passion for development
            </p>
          </motion.div>

          {/* Filter Buttons */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            className="flex flex-wrap justify-center gap-4 sm:gap-6"
          >
            <Button
              variant={filter === 'all' ? 'primary' : 'ghost'}
              size="md"
              onClick={() => setFilter('all')}
              className="rounded-full"
            >
              <FaFilter className="mr-2" />
              All Projects
            </Button>

            {allTechnologies.slice(0, 5).map((tech) => (
              <Button
                key={tech}
                variant={filter === tech ? 'primary' : 'ghost'}
                size="md"
                onClick={() => setFilter(tech)}
                className="rounded-full"
              >
                {tech}
              </Button>
            ))}
          </motion.div>

          {/* Projects Grid */}
          <AnimatePresence mode="wait">
            <motion.div
              key={filter}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="grid md:grid-cols-2 gap-8 lg:gap-12"
            >
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  onHoverStart={() => setHoveredProject(project.id)}
                  onHoverEnd={() => setHoveredProject(null)}
                  className="group relative"
                >
                  <Card
                    variant="glass"
                    size="md"
                    hover={true}
                    className="overflow-hidden h-full"
                  >
                    {/* Project Image Placeholder */}
                    <div className="relative h-48 bg-gradient-to-br from-primary/20 to-secondary/20 overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-br from-bg-tertiary to-bg-secondary opacity-80" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-6xl text-primary/30 font-bold">
                          {project.title.charAt(0)}
                        </div>
                      </div>
                      
                      {/* Hover Overlay */}
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: hoveredProject === project.id ? 1 : 0 }}
                        className="absolute inset-0 bg-bg-primary/80 flex items-center justify-center space-x-4"
                      >
                        <motion.a
                          href={project.github}
                          target="_blank"
                          rel="noopener noreferrer"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-3 bg-primary text-bg-primary rounded-full hover:bg-accent transition-colors"
                        >
                          <FaGithub size={20} />
                        </motion.a>
                        <motion.a
                          href={project.demo}
                          target="_blank"
                          rel="noopener noreferrer"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-3 bg-secondary text-white rounded-full hover:bg-secondary/80 transition-colors"
                        >
                          <FaExternalLinkAlt size={20} />
                        </motion.a>
                      </motion.div>

                      {/* Featured Badge */}
                      {project.featured && (
                        <div className="absolute top-4 right-4">
                          <span className="px-3 py-1 bg-primary/20 text-primary text-xs font-semibold rounded-full border border-primary/30 backdrop-blur-sm">
                            Featured
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Project Content */}
                    <div className="p-0">
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-primary transition-colors">
                        {project.title}
                      </h3>
                      
                      <p className="text-gray-300 text-sm leading-relaxed mb-4">
                        {project.description}
                      </p>

                      {/* Technologies */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.technologies.map((tech) => (
                          <span
                            key={tech}
                            className="px-2 py-1 bg-bg-tertiary text-primary text-xs font-medium rounded border border-primary/20"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>

                      {/* Links */}
                      <div className="flex space-x-4">
                        <a
                          href={project.github}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-gray-400 hover:text-primary transition-colors text-sm"
                        >
                          <FaGithub className="mr-2" />
                          Code
                        </a>
                        <a
                          href={project.demo}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-gray-400 hover:text-secondary transition-colors text-sm"
                        >
                          <FaExternalLinkAlt className="mr-2" />
                          Live Demo
                        </a>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </AnimatePresence>

          {/* View More */}
          <motion.div
            variants={ANIMATION_VARIANTS.fadeIn}
            className="text-center"
          >
            <Button
              variant="outline"
              size="lg"
              onClick={() => window.open('https://github.com/Talibshaib', '_blank')}
            >
              <FaGithub className="mr-2" />
              View More on GitHub
            </Button>
          </motion.div>
      </motion.div>
    </Section>
  );
}
