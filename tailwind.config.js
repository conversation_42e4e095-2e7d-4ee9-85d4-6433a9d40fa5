/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#00ffcc',
          50: '#f0fffe',
          100: '#ccfff7',
          200: '#99ffef',
          300: '#5cfde4',
          400: '#22f5d3',
          500: '#00ffcc',
          600: '#00d4aa',
          700: '#00a688',
          800: '#00826b',
          900: '#006b58',
        },
        secondary: {
          DEFAULT: '#ff3366',
          50: '#fff1f3',
          100: '#ffe1e7',
          200: '#ffc7d4',
          300: '#ff9fb1',
          400: '#ff6b8a',
          500: '#ff3366',
          600: '#f01650',
          700: '#d10a42',
          800: '#b10d3e',
          900: '#9a0f3c',
        },
        accent: {
          DEFAULT: '#00d4ff',
          50: '#f0fcff',
          100: '#e0f8ff',
          200: '#baf1ff',
          300: '#7de7ff',
          400: '#38daff',
          500: '#00d4ff',
          600: '#00a8d4',
          700: '#0086ab',
          800: '#00708d',
          900: '#065d74',
        },
        dark: {
          50: '#f8f8f8',
          100: '#e5e5e5',
          200: '#d1d1d1',
          300: '#b1b1b1',
          400: '#9e9e9e',
          500: '#7e7e7e',
          600: '#626262',
          700: '#515151',
          800: '#3b3b3b',
          900: '#2a2a2a',
          950: '#1a1a1a',
        },
        bg: {
          primary: '#0a0a0a',
          secondary: '#111111',
          tertiary: '#1a1a1a',
        }
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      fontSize: {
        'responsive-xl': 'clamp(2rem, 5vw, 4rem)',
        'responsive-lg': 'clamp(1.5rem, 4vw, 2.5rem)',
        'responsive-md': 'clamp(1.125rem, 2.5vw, 1.5rem)',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
        'gradient': 'gradient 3s ease infinite',
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'slide-down': 'slideDown 0.5s ease-out',
        'scale-in': 'scaleIn 0.3s ease-out',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        'pulse-glow': {
          '0%': { boxShadow: '0 0 5px var(--color-primary)' },
          '100%': { boxShadow: '0 0 20px var(--color-primary), 0 0 30px var(--color-primary)' },
        },
        gradient: {
          '0%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
          '100%': { backgroundPosition: '0% 50%' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, var(--color-primary), var(--color-accent))',
        'gradient-secondary': 'linear-gradient(135deg, var(--color-secondary), #ff6b9d)',
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      boxShadow: {
        'glow': '0 0 20px rgba(0, 255, 204, 0.3)',
        'glow-secondary': '0 0 20px rgba(255, 51, 102, 0.3)',
        'glow-lg': '0 0 40px rgba(0, 255, 204, 0.4)',
        'glow-xl': '0 0 60px rgba(0, 255, 204, 0.5)',
      },
      backdropBlur: {
        xs: '2px',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
        'xs': 'var(--spacing-xs)',
        'sm': 'var(--spacing-sm)',
        'md': 'var(--spacing-md)',
        'lg': 'var(--spacing-lg)',
        'xl': 'var(--spacing-xl)',
        '2xl': 'var(--spacing-2xl)',
        '3xl': 'var(--spacing-3xl)',
        '4xl': 'var(--spacing-4xl)',
      },
      maxWidth: {
        '8xl': '88rem',
        '9xl': '96rem',
      },
    },
  },
  plugins: [],
}
