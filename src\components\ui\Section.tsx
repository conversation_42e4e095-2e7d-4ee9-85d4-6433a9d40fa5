import { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import Container from './Container';

interface SectionProps {
  children: ReactNode;
  className?: string;
  containerClassName?: string;
  id?: string;
  background?: 'primary' | 'secondary' | 'tertiary' | 'transparent';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

const backgroundClasses = {
  primary: 'bg-bg-primary',
  secondary: 'bg-bg-secondary', 
  tertiary: 'bg-bg-tertiary',
  transparent: 'bg-transparent'
};

const paddingClasses = {
  none: '',
  sm: 'py-12',
  md: 'py-16', 
  lg: 'py-20',
  xl: 'py-24'
};

export default function Section({
  children,
  className = '',
  containerClassName = '',
  id,
  background = 'transparent',
  padding = 'lg',
  containerSize = 'xl'
}: SectionProps) {
  return (
    <section 
      id={id}
      className={cn(
        'relative overflow-hidden',
        backgroundClasses[background],
        paddingClasses[padding],
        className
      )}
    >
      <Container 
        size={containerSize}
        className={containerClassName}
      >
        {children}
      </Container>
    </section>
  );
}
