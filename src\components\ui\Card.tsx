import { ReactNode, forwardRef } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { cn } from '@/lib/utils';

interface CardProps extends Omit<HTMLMotionProps<"div">, 'children'> {
  children: ReactNode;
  variant?: 'default' | 'glass' | 'solid' | 'outline' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  glow?: boolean;
  className?: string;
}

const cardVariants = {
  default: 'bg-bg-secondary/50 border border-white/10',
  glass: 'bg-white/5 backdrop-blur-md border border-white/10',
  solid: 'bg-bg-secondary border border-white/20',
  outline: 'bg-transparent border-2 border-primary/30',
  gradient: 'bg-gradient-to-br from-primary/10 to-secondary/10 border border-white/10'
};

const cardSizes = {
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
  xl: 'p-10'
};

const Card = forwardRef<HTMLDivElement, CardProps>(({
  children,
  variant = 'default',
  size = 'md',
  hover = true,
  glow = false,
  className = '',
  ...props
}, ref) => {
  const baseClasses = cn(
    'rounded-xl transition-all duration-300',
    cardVariants[variant],
    cardSizes[size],
    {
      'hover:border-primary/50 hover:shadow-glow hover:-translate-y-1': hover,
      'shadow-glow': glow,
    },
    className
  );

  return (
    <motion.div
      ref={ref}
      className={baseClasses}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      {...props}
    >
      {children}
    </motion.div>
  );
});

Card.displayName = 'Card';

export default Card;
