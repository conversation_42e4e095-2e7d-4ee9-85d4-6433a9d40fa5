'use client';

import { motion } from 'framer-motion';
import { FaChevronDown, FaGithub, FaLinkedin, FaEnvelope } from 'react-icons/fa';
import { PERSONAL_INFO, ANIMATION_VARIANTS } from '@/lib/constants';
import ParticleBackground from '@/components/ui/ParticleBackground';
import AnimatedText from '@/components/ui/AnimatedText';
import Container from '@/components/ui/Container';
import dynamic from 'next/dynamic';

const SpaceScene = dynamic(() => import('@/components/3d/SpaceScene'), {
  ssr: false,
});

export default function Hero() {
  const scrollToNext = () => {
    const aboutSection = document.getElementById('about');
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen perfect-center overflow-hidden">
      {/* 3D Space Scene */}
      <SpaceScene />

      {/* Particle Background */}
      <ParticleBackground />

      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-tertiary opacity-90" />

      {/* Content */}
      <Container className="relative z-10 text-center">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={ANIMATION_VARIANTS.stagger}
          className="space-y-6 md:space-y-8"
        >
          {/* Greeting */}
          <motion.div
            variants={ANIMATION_VARIANTS.fadeIn}
            transition={{ duration: 0.6 }}
            className="space-y-2"
          >
            <motion.p
              variants={ANIMATION_VARIANTS.slideDown}
              className="text-lg md:text-xl text-gray-300 font-mono"
            >
              Hi, I&apos;m
            </motion.p>
          </motion.div>

          {/* Name */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <AnimatedText
              text={PERSONAL_INFO.name}
              className="text-responsive-xl font-bold text-gradient mb-4"
            />
          </motion.div>

          {/* Title */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <AnimatedText
              text={PERSONAL_INFO.title}
              className="text-responsive-lg font-semibold text-white mb-6"
              delay={0.5}
            />
          </motion.div>

          {/* Subtitle */}
          <motion.p
            variants={ANIMATION_VARIANTS.fadeIn}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-responsive-md text-gray-300 max-w-2xl mx-auto leading-relaxed"
          >
            {PERSONAL_INFO.subtitle}
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            transition={{ duration: 0.6, delay: 1.0 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <motion.button
              whileHover={{ scale: 1.05, boxShadow: "0 0 25px rgba(0, 255, 204, 0.5)" }}
              whileTap={{ scale: 0.95 }}
              onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
              className="btn-primary"
            >
              View My Work
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="btn-secondary"
            >
              Get In Touch
            </motion.button>
          </motion.div>

          {/* Social Links */}
          <motion.div
            variants={ANIMATION_VARIANTS.fadeIn}
            transition={{ duration: 0.6, delay: 1.2 }}
            className="flex justify-center space-x-6"
          >
            <motion.a
              href={PERSONAL_INFO.github}
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.2, color: "#00ffcc" }}
              whileTap={{ scale: 0.9 }}
              className="text-gray-400 hover:text-primary transition-colors duration-300"
            >
              <FaGithub size={24} />
            </motion.a>
            
            <motion.a
              href={PERSONAL_INFO.linkedin}
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.2, color: "#00ffcc" }}
              whileTap={{ scale: 0.9 }}
              className="text-gray-400 hover:text-primary transition-colors duration-300"
            >
              <FaLinkedin size={24} />
            </motion.a>
            
            <motion.a
              href={`mailto:${PERSONAL_INFO.email}`}
              whileHover={{ scale: 1.2, color: "#00ffcc" }}
              whileTap={{ scale: 0.9 }}
              className="text-gray-400 hover:text-primary transition-colors duration-300"
            >
              <FaEnvelope size={24} />
            </motion.a>
          </motion.div>
        </motion.div>
      </Container>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.button
          onClick={scrollToNext}
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-gray-400 hover:text-primary transition-colors duration-300 flex flex-col items-center space-y-2"
        >
          <span className="text-sm font-mono">Scroll Down</span>
          <FaChevronDown size={20} />
        </motion.button>
      </motion.div>
    </section>
  );
}
