'use client';

import { motion } from 'framer-motion';
import { FaHeart, FaRocket, FaGithub, FaLinkedin, FaEnvelope } from 'react-icons/fa';
import { PERSONAL_INFO, NAVIGATION } from '@/lib/constants';
import Container from '@/components/ui/Container';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  const handleNavClick = (href: string) => {
    const element = document.getElementById(href.substring(1));
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-bg-secondary border-t border-white/10 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-t from-primary/10 to-transparent" />
      </div>

      <Container className="relative z-10 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="md:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold text-gradient mb-4">
                {PERSONAL_INFO.name}
              </h3>
              <p className="text-gray-300 mb-4 max-w-md">
                {PERSONAL_INFO.title}
              </p>
              <p className="text-gray-400 text-sm mb-6">
                Passionate about creating innovative solutions and exploring the frontiers of space science.
              </p>
              
              {/* Social Links */}
              <div className="flex space-x-4">
                <motion.a
                  href={PERSONAL_INFO.github}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-10 h-10 bg-bg-tertiary rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-primary/20 transition-all duration-300"
                >
                  <FaGithub size={18} />
                </motion.a>
                
                <motion.a
                  href={PERSONAL_INFO.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-10 h-10 bg-bg-tertiary rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-primary/20 transition-all duration-300"
                >
                  <FaLinkedin size={18} />
                </motion.a>
                
                <motion.a
                  href={`mailto:${PERSONAL_INFO.email}`}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-10 h-10 bg-bg-tertiary rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-primary/20 transition-all duration-300"
                >
                  <FaEnvelope size={18} />
                </motion.a>
              </div>
            </motion.div>
          </div>

          {/* Quick Links */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold text-white mb-4">Quick Links</h4>
              <ul className="space-y-2">
                {NAVIGATION.map((item) => (
                  <li key={item.name}>
                    <motion.button
                      onClick={() => handleNavClick(item.href)}
                      whileHover={{ x: 5 }}
                      className="text-gray-400 hover:text-primary transition-all duration-300 text-sm"
                    >
                      {item.name}
                    </motion.button>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Contact Info */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold text-white mb-4">Contact</h4>
              <div className="space-y-2">
                <p className="text-gray-400 text-sm">
                  <span className="text-primary">Email:</span><br />
                  <a 
                    href={`mailto:${PERSONAL_INFO.email}`}
                    className="hover:text-primary transition-colors"
                  >
                    {PERSONAL_INFO.email}
                  </a>
                </p>
                <p className="text-gray-400 text-sm">
                  <span className="text-primary">Location:</span><br />
                  {PERSONAL_INFO.location}
                </p>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-12 pt-8 border-t border-white/10"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center text-gray-400 text-sm">
              <span>© {currentYear} {PERSONAL_INFO.name}. Made with</span>
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="mx-1"
              >
                <FaHeart className="text-secondary" />
              </motion.div>
              <span>and</span>
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="mx-1"
              >
                <FaRocket className="text-primary" />
              </motion.div>
            </div>
            
            <div className="text-gray-400 text-sm">
              <span>Inspired by </span>
              <a 
                href="https://dunks1980.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary hover:text-accent transition-colors"
              >
                dunks1980.com
              </a>
            </div>
          </div>
        </motion.div>
      </Container>
    </footer>
  );
}
