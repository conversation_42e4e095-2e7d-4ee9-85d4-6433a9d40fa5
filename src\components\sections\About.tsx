'use client';

import { motion } from 'framer-motion';
import { FaGraduationCap, FaMapMarkerAlt, FaRocket } from 'react-icons/fa';
import { PERSONAL_INFO, EDUCATION, SKILLS, ANIMATION_VARIANTS } from '@/lib/constants';
import Section from '@/components/ui/Section';
import Card from '@/components/ui/Card';

export default function About() {
  const allSkills = [
    ...SKILLS.languages,
    ...SKILLS.frontend,
    ...SKILLS.backend,
    ...SKILLS.databases,
    ...SKILLS.blockchain,
    ...SKILLS.tools,
  ];

  return (
    <Section
      id="about"
      background="secondary"
      className="relative"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20" />
      </div>
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={ANIMATION_VARIANTS.stagger}
        className="relative z-10 space-y-20"
      >
          {/* Section Header */}
          <motion.div
            variants={ANIMATION_VARIANTS.fadeIn}
            className="text-center"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gradient mb-4">
              About Me
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Passionate about technology, space science, and creating innovative solutions
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-start">
            {/* Left Column - Personal Info */}
            <motion.div
              variants={ANIMATION_VARIANTS.slideLeft}
              className="space-y-8"
            >
              {/* Bio */}
              <Card variant="glass" size="lg">
                <h3 className="text-2xl font-semibold text-white mb-6 flex items-center">
                  <FaRocket className="text-primary mr-3" />
                  My Journey
                </h3>
                <div className="space-y-4">
                  <p className="text-gray-300 leading-relaxed">
                    I&apos;m a passionate developer with a deep fascination for space science and cutting-edge technology.
                    My journey in programming started with curiosity and has evolved into a mission to build
                    innovative solutions that can make a difference.
                  </p>
                  <p className="text-gray-300 leading-relaxed">
                    When I&apos;m not coding, you&apos;ll find me exploring the latest developments in space technology,
                    contributing to open-source projects, or dreaming about the future of human space exploration.
                  </p>
                </div>
              </Card>

              {/* Personal Details */}
              <Card variant="glass" size="lg">
                <h3 className="text-2xl font-semibold text-white mb-6">Personal Details</h3>
                <div className="space-y-6">
                  <div className="flex items-center text-gray-300">
                    <FaMapMarkerAlt className="text-primary mr-4 flex-shrink-0 text-lg" />
                    <span className="text-lg">{PERSONAL_INFO.location}</span>
                  </div>
                  <div className="flex items-center text-gray-300">
                    <FaGraduationCap className="text-primary mr-4 flex-shrink-0 text-lg" />
                    <span className="text-lg">{EDUCATION.institution}</span>
                  </div>
                  <div className="text-gray-300 ml-8">
                    <span className="font-medium text-lg text-primary">{EDUCATION.degree}</span> • {EDUCATION.duration}
                  </div>
                  <div className="text-gray-300 ml-8">
                    <span className="font-medium text-primary">CGPA:</span> {EDUCATION.cgpa}
                  </div>
                </div>
              </Card>
            </motion.div>

            {/* Right Column - Skills */}
            <motion.div
              variants={ANIMATION_VARIANTS.slideRight}
              className="space-y-8"
            >
              {/* Skills Grid */}
              <Card variant="glass" size="lg">
                <h3 className="text-2xl font-semibold text-white mb-8">Technical Skills</h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                  {allSkills.map((skill, index) => (
                    <motion.div
                      key={skill}
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      whileHover={{ scale: 1.05, boxShadow: "0 0 15px rgba(0, 255, 204, 0.3)" }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="bg-bg-tertiary p-4 rounded-lg text-center border border-white/10 hover:border-primary/50 transition-all duration-300"
                    >
                      <span className="text-sm font-medium text-gray-300 hover:text-white transition-colors">
                        {skill}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </Card>

              {/* Skill Categories */}
              <div className="space-y-6">
                {[
                  { title: "Languages", skills: SKILLS.languages, color: "text-primary" },
                  { title: "Frontend", skills: SKILLS.frontend, color: "text-secondary" },
                  { title: "Backend", skills: SKILLS.backend, color: "text-accent" },
                  { title: "Databases", skills: SKILLS.databases, color: "text-primary" },
                ].map((category, index) => (
                  <motion.div
                    key={category.title}
                    initial={{ opacity: 0, x: 50 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="glass p-6 rounded-lg"
                  >
                    <h4 className={`font-semibold mb-2 ${category.color}`}>
                      {category.title}
                    </h4>
                    <p className="text-gray-300 text-sm">
                      {category.skills.join(" • ")}
                    </p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
      </motion.div>
    </Section>
  );
}
