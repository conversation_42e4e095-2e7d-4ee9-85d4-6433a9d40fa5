// Personal Information
export const PERSONAL_INFO = {
  name: "<PERSON><PERSON><PERSON> Shaikh",
  title: "<PERSON><PERSON><PERSON>, Tech Enthusiast, Future Space Scientist",
  subtitle: "Building the future with code and innovation",
  email: "<EMAIL>",
  location: "India",
  github: "https://github.com/Talibshaib",
  linkedin: "https://linkedin.com/in/talibshaib", // Update with actual LinkedIn
} as const;

// Education Information
export const EDUCATION = {
  institution: "Technocrats Institute of Technology",
  degree: "B.Tech",
  duration: "2022-2026",
  cgpa: "7.5+",
} as const;

// Skills
export const SKILLS = {
  languages: ["C++", "C", "TypeScript", "Python", "JavaScript"],
  frontend: ["React", "Next.js", "React Native"],
  backend: ["Node.js", "Express"],
  databases: ["MongoDB", "MySQL", "Mongoose"],
  blockchain: ["Basic Solidity"],
  tools: ["Git", "GitHub"],
} as const;

// Experience
export const EXPERIENCE = [
  {
    id: 1,
    company: "Gok Apture Event Technology",
    position: "Frontend Developer",
    duration: "Sep 2024 - Present",
    description: "Developed and optimized interactive web components, boosting global user engagement. Enhanced UI/UX design performance.",
    technologies: ["React", "TypeScript", "JavaScript"],
    current: true,
  },
  {
    id: 2,
    company: "GapAssess Solution",
    position: "App Developer",
    duration: "Oct 2018 - Feb 2019",
    description: "Developed delivery tracking app with offline caching. Created recommendation system app to improve user experience.",
    technologies: ["React Native", "JavaScript"],
    current: false,
  },
] as const;

// Projects (Placeholder structure)
export const PROJECTS = [
  {
    id: 1,
    title: "Project Alpha",
    description: "BIT EXPLANATION POINTS POINTS - Advanced web application with modern features",
    technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS"],
    github: "https://github.com/Talibshaib/project-alpha",
    demo: "https://project-alpha.vercel.app",
    image: "/assets/projects/project-alpha.jpg",
    featured: true,
  },
  {
    id: 2,
    title: "Project Beta",
    description: "Innovative mobile application with cutting-edge technology",
    technologies: ["React Native", "Node.js", "MongoDB"],
    github: "https://github.com/Talibshaib/project-beta",
    demo: "https://project-beta.vercel.app",
    image: "/assets/projects/project-beta.jpg",
    featured: true,
  },
  {
    id: 3,
    title: "Project Gamma",
    description: "Full-stack solution with real-time capabilities",
    technologies: ["Python", "Express", "MySQL"],
    github: "https://github.com/Talibshaib/project-gamma",
    demo: "https://project-gamma.vercel.app",
    image: "/assets/projects/project-gamma.jpg",
    featured: false,
  },
  {
    id: 4,
    title: "Project Delta",
    description: "Blockchain-based application with smart contracts",
    technologies: ["Solidity", "React", "Web3.js"],
    github: "https://github.com/Talibshaib/project-delta",
    demo: "https://project-delta.vercel.app",
    image: "/assets/projects/project-delta.jpg",
    featured: false,
  },
] as const;

// Navigation
export const NAVIGATION = [
  { name: "Home", href: "#home" },
  { name: "About", href: "#about" },
  { name: "Experience", href: "#experience" },
  { name: "Projects", href: "#projects" },
  { name: "Contact", href: "#contact" },
] as const;

// Social Links
export const SOCIAL_LINKS = [
  {
    name: "GitHub",
    url: PERSONAL_INFO.github,
    icon: "FaGithub",
  },
  {
    name: "LinkedIn",
    url: PERSONAL_INFO.linkedin,
    icon: "FaLinkedin",
  },
  {
    name: "Email",
    url: `mailto:${PERSONAL_INFO.email}`,
    icon: "FaEnvelope",
  },
] as const;

// Animation Variants
export const ANIMATION_VARIANTS = {
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  },
  slideUp: {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0 },
  },
  slideDown: {
    hidden: { opacity: 0, y: -50 },
    visible: { opacity: 1, y: 0 },
  },
  slideLeft: {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0 },
  },
  slideRight: {
    hidden: { opacity: 0, x: -50 },
    visible: { opacity: 1, x: 0 },
  },
  scale: {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1 },
  },
  stagger: {
    visible: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  },
} as const;
